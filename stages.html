<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Browse thousands of internship opportunities from top companies worldwide">
  <meta name="keywords" content="internships, stages, jobs, students, career opportunities">

  <title>Browse Stages - Univio</title>

  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="preconnect" href="https://cdn.jsdelivr.net">

  <!-- Modern fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- Bootstrap 5.3 -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

  <!-- Custom styles -->
  <link rel="stylesheet" href="style.css">

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
</head>
<body>
  <!-- Skip to main content for accessibility -->
  <a href="#main-content" class="visually-hidden-focusable">Skip to main content</a>

  <!-- Modern Navigation -->
  <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top" aria-label="Main navigation">
    <div class="container">
      <a class="navbar-brand d-flex align-items-center" href="index.html" aria-label="Univio Home">
        <div class="brand-icon me-2">
          <i class="bi bi-rocket-takeoff-fill text-primary fs-3"></i>
        </div>
        <span class="fw-bold fs-4 text-primary">Univio</span>
      </a>

      <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navMenu"
              aria-controls="navMenu" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>

      <div class="collapse navbar-collapse" id="navMenu">
        <ul class="navbar-nav ms-auto align-items-lg-center">
          <li class="nav-item">
            <a class="nav-link fw-medium px-3 active" href="stages.html">
              <i class="bi bi-briefcase me-1"></i>Stages
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link fw-medium px-3" href="events.html">
              <i class="bi bi-calendar-event me-1"></i>Events
            </a>
          </li>
          <li class="nav-item ms-lg-2">
            <a class="nav-link btn btn-outline-primary px-4 rounded-pill" href="login.html">
              <i class="bi bi-person me-1"></i>Login
            </a>
          </li>
          <li class="nav-item ms-lg-2">
            <a class="nav-link btn btn-primary text-white px-4 rounded-pill" href="register.html">
              Sign Up
            </a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <!-- Page Header -->
  <section class="page-header bg-light py-5" id="main-content">
    <div class="container">
      <div class="row align-items-center">
        <div class="col-lg-8">
          <h1 class="display-5 fw-bold mb-3">Find Your Perfect Stage</h1>
          <p class="lead text-muted mb-4">
            Discover amazing internship opportunities from top companies worldwide.
            Filter by location, duration, and skills to find your ideal match.
          </p>
          <div class="d-flex gap-3 flex-wrap">
            <span class="badge bg-primary-subtle text-primary px-3 py-2">
              <i class="bi bi-briefcase me-1"></i>1,200+ Active Positions
            </span>
            <span class="badge bg-success-subtle text-success px-3 py-2">
              <i class="bi bi-building me-1"></i>500+ Companies
            </span>
            <span class="badge bg-info-subtle text-info px-3 py-2">
              <i class="bi bi-globe me-1"></i>50+ Countries
            </span>
          </div>
        </div>
        <div class="col-lg-4 text-center">
          <div class="header-illustration">
            <i class="bi bi-search text-primary" style="font-size: 8rem; opacity: 0.1;"></i>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Advanced Filter Section -->
  <section class="filter-section py-4 bg-white shadow-sm">
    <div class="container">
      <form class="filter-form" id="stageFilters">
        <div class="row g-3 align-items-end">
          <div class="col-lg-3 col-md-6">
            <label for="keyword" class="form-label fw-medium">
              <i class="bi bi-search me-1"></i>Keywords
            </label>
            <input type="text" class="form-control" id="keyword"
                   placeholder="AI, Web Development, Data...">
          </div>

          <div class="col-lg-2 col-md-6">
            <label for="location" class="form-label fw-medium">
              <i class="bi bi-geo-alt me-1"></i>Location
            </label>
            <select class="form-select" id="location">
              <option value="">All Locations</option>
              <option value="remote">Remote</option>
              <option value="paris">Paris, France</option>
              <option value="berlin">Berlin, Germany</option>
              <option value="london">London, UK</option>
              <option value="madrid">Madrid, Spain</option>
              <option value="amsterdam">Amsterdam, Netherlands</option>
            </select>
          </div>

          <div class="col-lg-2 col-md-6">
            <label for="duration" class="form-label fw-medium">
              <i class="bi bi-clock me-1"></i>Duration
            </label>
            <select class="form-select" id="duration">
              <option value="">Any Duration</option>
              <option value="1-3">1-3 months</option>
              <option value="3-6">3-6 months</option>
              <option value="6-12">6-12 months</option>
              <option value="12+">12+ months</option>
            </select>
          </div>

          <div class="col-lg-2 col-md-6">
            <label for="field" class="form-label fw-medium">
              <i class="bi bi-tag me-1"></i>Field
            </label>
            <select class="form-select" id="field">
              <option value="">All Fields</option>
              <option value="tech">Technology</option>
              <option value="marketing">Marketing</option>
              <option value="finance">Finance</option>
              <option value="design">Design</option>
              <option value="engineering">Engineering</option>
              <option value="research">Research</option>
            </select>
          </div>

          <div class="col-lg-2 col-md-6">
            <label for="company-size" class="form-label fw-medium">
              <i class="bi bi-building me-1"></i>Company Size
            </label>
            <select class="form-select" id="company-size">
              <option value="">Any Size</option>
              <option value="startup">Startup (1-50)</option>
              <option value="medium">Medium (51-500)</option>
              <option value="large">Large (500+)</option>
            </select>
          </div>

          <div class="col-lg-1 col-md-6">
            <button type="submit" class="btn btn-primary w-100 rounded-pill">
              <i class="bi bi-funnel"></i>
            </button>
          </div>
        </div>

        <!-- Quick Filters -->
        <div class="row mt-3">
          <div class="col-12">
            <div class="d-flex gap-2 flex-wrap">
              <span class="text-muted small me-2">Quick filters:</span>
              <button type="button" class="btn btn-outline-primary btn-sm rounded-pill quick-filter" data-filter="remote">
                Remote Only
              </button>
              <button type="button" class="btn btn-outline-primary btn-sm rounded-pill quick-filter" data-filter="tech">
                Tech Roles
              </button>
              <button type="button" class="btn btn-outline-primary btn-sm rounded-pill quick-filter" data-filter="paid">
                Paid Internships
              </button>
              <button type="button" class="btn btn-outline-primary btn-sm rounded-pill quick-filter" data-filter="immediate">
                Immediate Start
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </section>

  <!-- Results Section -->
  <section class="results-section py-5">
    <div class="container">
      <div class="row">
        <!-- Sidebar Filters (Desktop) -->
        <div class="col-lg-3 d-none d-lg-block">
          <div class="filter-sidebar sticky-top" style="top: 100px;">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                  <i class="bi bi-sliders me-2"></i>Refine Results
                </h5>
              </div>
              <div class="card-body">
                <!-- Salary Range -->
                <div class="filter-group mb-4">
                  <h6 class="fw-medium mb-3">Salary Range (Monthly)</h6>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="unpaid">
                    <label class="form-check-label" for="unpaid">Unpaid</label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="salary-500">
                    <label class="form-check-label" for="salary-500">€500 - €1,000</label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="salary-1000">
                    <label class="form-check-label" for="salary-1000">€1,000 - €1,500</label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="salary-1500">
                    <label class="form-check-label" for="salary-1500">€1,500+</label>
                  </div>
                </div>

                <!-- Start Date -->
                <div class="filter-group mb-4">
                  <h6 class="fw-medium mb-3">Start Date</h6>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="immediate">
                    <label class="form-check-label" for="immediate">Immediate</label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="next-month">
                    <label class="form-check-label" for="next-month">Next Month</label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="flexible">
                    <label class="form-check-label" for="flexible">Flexible</label>
                  </div>
                </div>

                <!-- Work Type -->
                <div class="filter-group mb-4">
                  <h6 class="fw-medium mb-3">Work Type</h6>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="full-time">
                    <label class="form-check-label" for="full-time">Full-time</label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="part-time">
                    <label class="form-check-label" for="part-time">Part-time</label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="project-based">
                    <label class="form-check-label" for="project-based">Project-based</label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Main Results -->
        <div class="col-lg-9">
          <!-- Results Header -->
          <div class="results-header d-flex justify-content-between align-items-center mb-4">
            <div>
              <h4 class="mb-1">Available Stages</h4>
              <p class="text-muted mb-0">Showing <span id="results-count">1,247</span> results</p>
            </div>
            <div class="d-flex gap-2 align-items-center">
              <span class="text-muted small">Sort by:</span>
              <select class="form-select form-select-sm" style="width: auto;">
                <option>Most Recent</option>
                <option>Best Match</option>
                <option>Salary: High to Low</option>
                <option>Salary: Low to High</option>
                <option>Company A-Z</option>
              </select>
            </div>
          </div>

          <!-- Stage Listings -->
          <div class="stage-listings">
            <div class="row g-4">
              <!-- Stage Card 1 -->
              <div class="col-12">
                <article class="stage-card card border-0 shadow-sm hover-lift">
                  <div class="card-body p-4">
                    <div class="row align-items-center">
                      <div class="col-md-8">
                        <div class="d-flex align-items-start mb-3">
                          <div class="company-logo bg-primary bg-gradient rounded-3 p-2 me-3">
                            <i class="bi bi-robot text-white fs-4"></i>
                          </div>
                          <div class="flex-grow-1">
                            <div class="d-flex align-items-center gap-2 mb-1">
                              <h3 class="h5 fw-bold mb-0">AI Research Intern</h3>
                              <span class="badge bg-success rounded-pill">New</span>
                              <span class="badge bg-warning text-dark rounded-pill">Featured</span>
                            </div>
                            <p class="text-muted mb-2">
                              <i class="bi bi-building me-1"></i>TechCorp
                              <span class="mx-2">•</span>
                              <i class="bi bi-geo-alt me-1"></i>Paris, France
                              <span class="mx-2">•</span>
                              <i class="bi bi-clock me-1"></i>6 months
                            </p>
                            <p class="mb-3">Join our cutting-edge AI research team to work on machine learning models and contribute to groundbreaking projects in artificial intelligence.</p>
                            <div class="tags">
                              <span class="badge bg-light text-dark me-1">Machine Learning</span>
                              <span class="badge bg-light text-dark me-1">Python</span>
                              <span class="badge bg-light text-dark me-1">TensorFlow</span>
                              <span class="badge bg-light text-dark">Research</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-4 text-md-end">
                        <div class="mb-2">
                          <span class="text-success fw-bold fs-5">€1,200/month</span>
                        </div>
                        <div class="mb-3">
                          <small class="text-muted">Posted 2 days ago</small>
                        </div>
                        <div class="d-flex gap-2 justify-content-md-end">
                          <button class="btn btn-outline-primary btn-sm rounded-pill">
                            <i class="bi bi-heart"></i>
                          </button>
                          <a href="stagedetail.html" class="btn btn-primary rounded-pill">
                            <i class="bi bi-arrow-right me-1"></i>View Details
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
              </div>

              <!-- Stage Card 2 -->
              <div class="col-12">
                <article class="stage-card card border-0 shadow-sm hover-lift">
                  <div class="card-body p-4">
                    <div class="row align-items-center">
                      <div class="col-md-8">
                        <div class="d-flex align-items-start mb-3">
                          <div class="company-logo bg-info bg-gradient rounded-3 p-2 me-3">
                            <i class="bi bi-code-slash text-white fs-4"></i>
                          </div>
                          <div class="flex-grow-1">
                            <div class="d-flex align-items-center gap-2 mb-1">
                              <h3 class="h5 fw-bold mb-0">Full Stack Developer</h3>
                              <span class="badge bg-warning text-dark rounded-pill">Remote</span>
                            </div>
                            <p class="text-muted mb-2">
                              <i class="bi bi-building me-1"></i>Webify Solutions
                              <span class="mx-2">•</span>
                              <i class="bi bi-globe me-1"></i>Remote Worldwide
                              <span class="mx-2">•</span>
                              <i class="bi bi-clock me-1"></i>3 months
                            </p>
                            <p class="mb-3">Build modern web applications using React, Node.js, and MongoDB. Work with a distributed team on exciting client projects.</p>
                            <div class="tags">
                              <span class="badge bg-light text-dark me-1">React</span>
                              <span class="badge bg-light text-dark me-1">Node.js</span>
                              <span class="badge bg-light text-dark me-1">MongoDB</span>
                              <span class="badge bg-light text-dark">JavaScript</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-4 text-md-end">
                        <div class="mb-2">
                          <span class="text-success fw-bold fs-5">€800/month</span>
                        </div>
                        <div class="mb-3">
                          <small class="text-muted">Posted 1 week ago</small>
                        </div>
                        <div class="d-flex gap-2 justify-content-md-end">
                          <button class="btn btn-outline-primary btn-sm rounded-pill">
                            <i class="bi bi-heart"></i>
                          </button>
                          <a href="stagedetail.html" class="btn btn-primary rounded-pill">
                            <i class="bi bi-arrow-right me-1"></i>View Details
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
              </div>

              <!-- Stage Card 3 -->
              <div class="col-12">
                <article class="stage-card card border-0 shadow-sm hover-lift">
                  <div class="card-body p-4">
                    <div class="row align-items-center">
                      <div class="col-md-8">
                        <div class="d-flex align-items-start mb-3">
                          <div class="company-logo bg-success bg-gradient rounded-3 p-2 me-3">
                            <i class="bi bi-graph-up text-white fs-4"></i>
                          </div>
                          <div class="flex-grow-1">
                            <div class="d-flex align-items-center gap-2 mb-1">
                              <h3 class="h5 fw-bold mb-0">Data Analyst Intern</h3>
                              <span class="badge bg-primary rounded-pill">Popular</span>
                            </div>
                            <p class="text-muted mb-2">
                              <i class="bi bi-building me-1"></i>DataX Analytics
                              <span class="mx-2">•</span>
                              <i class="bi bi-geo-alt me-1"></i>Berlin, Germany
                              <span class="mx-2">•</span>
                              <i class="bi bi-clock me-1"></i>4 months
                            </p>
                            <p class="mb-3">Analyze large datasets, create visualizations, and provide insights to drive business decisions using SQL, Python, and Tableau.</p>
                            <div class="tags">
                              <span class="badge bg-light text-dark me-1">SQL</span>
                              <span class="badge bg-light text-dark me-1">Python</span>
                              <span class="badge bg-light text-dark me-1">Tableau</span>
                              <span class="badge bg-light text-dark">Statistics</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-4 text-md-end">
                        <div class="mb-2">
                          <span class="text-success fw-bold fs-5">€1,000/month</span>
                        </div>
                        <div class="mb-3">
                          <small class="text-muted">Posted 3 days ago</small>
                        </div>
                        <div class="d-flex gap-2 justify-content-md-end">
                          <button class="btn btn-outline-primary btn-sm rounded-pill">
                            <i class="bi bi-heart"></i>
                          </button>
                          <a href="stagedetail.html" class="btn btn-primary rounded-pill">
                            <i class="bi bi-arrow-right me-1"></i>View Details
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
              </div>
            </div>

            <!-- Pagination -->
            <nav aria-label="Stage listings pagination" class="mt-5">
              <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                  <a class="page-link" href="#" tabindex="-1" aria-disabled="true">
                    <i class="bi bi-chevron-left"></i>
                  </a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item"><a class="page-link" href="#">...</a></li>
                <li class="page-item"><a class="page-link" href="#">42</a></li>
                <li class="page-item">
                  <a class="page-link" href="#">
                    <i class="bi bi-chevron-right"></i>
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Modern Footer -->
  <footer class="footer bg-dark text-white py-5">
    <div class="container">
      <div class="row g-4">
        <div class="col-lg-4 col-md-6">
          <div class="footer-brand mb-4">
            <div class="d-flex align-items-center mb-3">
              <i class="bi bi-rocket-takeoff-fill text-primary fs-3 me-2"></i>
              <span class="fw-bold fs-4">Univio</span>
            </div>
            <p class="text-white-75">
              Connecting students and professionals with amazing internship and career opportunities worldwide.
            </p>
            <div class="social-links d-flex gap-3">
              <a href="#" class="social-link" aria-label="Follow us on Twitter">
                <i class="bi bi-twitter"></i>
              </a>
              <a href="#" class="social-link" aria-label="Follow us on LinkedIn">
                <i class="bi bi-linkedin"></i>
              </a>
              <a href="#" class="social-link" aria-label="Follow us on Instagram">
                <i class="bi bi-instagram"></i>
              </a>
            </div>
          </div>
        </div>

        <div class="col-lg-2 col-md-6">
          <h5 class="footer-title fw-bold mb-3">For Students</h5>
          <ul class="footer-links list-unstyled">
            <li><a href="stages.html" class="footer-link">Browse Stages</a></li>
            <li><a href="events.html" class="footer-link">Find Events</a></li>
            <li><a href="register.html" class="footer-link">Sign Up</a></li>
          </ul>
        </div>

        <div class="col-lg-2 col-md-6">
          <h5 class="footer-title fw-bold mb-3">For Companies</h5>
          <ul class="footer-links list-unstyled">
            <li><a href="companydashboard.html" class="footer-link">Post Stages</a></li>
            <li><a href="#" class="footer-link">Find Talent</a></li>
          </ul>
        </div>

        <div class="col-lg-2 col-md-6">
          <h5 class="footer-title fw-bold mb-3">Support</h5>
          <ul class="footer-links list-unstyled">
            <li><a href="#" class="footer-link">Help Center</a></li>
            <li><a href="#" class="footer-link">Contact Us</a></li>
          </ul>
        </div>

        <div class="col-lg-2 col-md-6">
          <h5 class="footer-title fw-bold mb-3">Legal</h5>
          <ul class="footer-links list-unstyled">
            <li><a href="#" class="footer-link">Privacy Policy</a></li>
            <li><a href="#" class="footer-link">Terms of Service</a></li>
          </ul>
        </div>
      </div>

      <hr class="footer-divider my-4 border-secondary">

      <div class="row align-items-center">
        <div class="col-md-6">
          <p class="footer-copyright text-white-75 mb-0">
            &copy; 2025 Univio. All rights reserved.
          </p>
        </div>
        <div class="col-md-6 text-md-end">
          <div class="footer-meta text-white-75">
            <span>🌍 Available worldwide</span>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- Back to top button -->
  <button class="back-to-top btn btn-primary rounded-circle position-fixed" id="backToTop" aria-label="Back to top">
    <i class="bi bi-arrow-up"></i>
  </button>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

  <script>
    // Back to top functionality
    const backToTopButton = document.getElementById('backToTop');

    window.addEventListener('scroll', () => {
      if (window.pageYOffset > 300) {
        backToTopButton.style.display = 'block';
      } else {
        backToTopButton.style.display = 'none';
      }
    });

    backToTopButton.addEventListener('click', () => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    // Quick filters functionality
    document.querySelectorAll('.quick-filter').forEach(button => {
      button.addEventListener('click', function() {
        this.classList.toggle('active');
        // Add filter logic here
      });
    });

    // Filter form submission
    document.getElementById('stageFilters').addEventListener('submit', function(e) {
      e.preventDefault();
      // Add filter logic here
      console.log('Filters applied');
    });

    // Favorite button functionality
    document.querySelectorAll('.btn-outline-primary').forEach(button => {
      if (button.querySelector('.bi-heart')) {
        button.addEventListener('click', function(e) {
          e.preventDefault();
          const icon = this.querySelector('i');
          if (icon.classList.contains('bi-heart')) {
            icon.classList.remove('bi-heart');
            icon.classList.add('bi-heart-fill');
            this.classList.remove('btn-outline-primary');
            this.classList.add('btn-primary');
          } else {
            icon.classList.remove('bi-heart-fill');
            icon.classList.add('bi-heart');
            this.classList.remove('btn-primary');
            this.classList.add('btn-outline-primary');
          }
        });
      }
    });
  </script>
</body>
</html>
