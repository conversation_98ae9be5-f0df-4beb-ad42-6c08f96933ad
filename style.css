/* Modern CSS Variables */
:root {
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --success-color: #198754;
  --info-color: #0dcaf0;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --light-color: #f8f9fa;
  --dark-color: #212529;

  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-info: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;

  --border-radius: 0.75rem;
  --border-radius-lg: 1rem;
  --border-radius-xl: 1.5rem;

  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--dark-color);
  overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  line-height: 1.2;
}

/* Utility Classes */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-white-75 {
  color: rgba(255, 255, 255, 0.75) !important;
}

.bg-gradient-primary {
  background: var(--gradient-primary) !important;
}

.bg-gradient-secondary {
  background: var(--gradient-secondary) !important;
}

.bg-gradient-success {
  background: var(--gradient-success) !important;
}

.bg-gradient-info {
  background: var(--gradient-info) !important;
}

/* Animations */
@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.animate-fade-up {
  animation: fadeUp 0.8s ease-out forwards;
  opacity: 0;
}

/* Navigation Styles */
.navbar {
  backdrop-filter: blur(10px);
  transition: var(--transition);
}

.navbar-brand {
  font-family: var(--font-heading);
  font-size: 1.5rem;
  font-weight: 700;
}

.brand-icon {
  transition: var(--transition);
}

.navbar-brand:hover .brand-icon {
  transform: rotate(10deg) scale(1.1);
}

.nav-link {
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.nav-link:hover {
  transform: translateY(-2px);
}

.nav-link.btn {
  transition: var(--transition);
}

.nav-link.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  z-index: 1;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 2;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  font-size: clamp(1.1rem, 2vw, 1.25rem);
  max-width: 600px;
  margin: 0 auto 2rem;
}

/* Search Form */
.search-form {
  max-width: 600px;
  margin: 0 auto;
}

.search-container {
  transition: var(--transition);
}

.search-container:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.search-container .form-control {
  font-size: 1rem;
  padding: 0.75rem 1rem;
}

.search-container .form-control:focus {
  box-shadow: none;
  border-color: transparent;
}

/* Hero Stats */
.hero-stats {
  margin-top: 4rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Floating Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.floating-element {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.floating-element-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-element-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.floating-element-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

/* Section Styles */
.section-title {
  font-weight: 700;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

/* Card Hover Effects */
.hover-lift {
  transition: var(--transition);
  cursor: pointer;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
}

/* Stage Cards */
.stage-card {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  transition: var(--transition);
}

.stage-card .card-header {
  padding: 1.5rem 1.5rem 0;
}

.company-logo {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius);
}

.company-info {
  font-size: 0.9rem;
}

.tags .badge {
  font-size: 0.75rem;
  padding: 0.4rem 0.8rem;
  border-radius: var(--border-radius);
  font-weight: 500;
}

/* Event Cards */
.event-card {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  transition: var(--transition);
}

.event-image-container {
  position: relative;
}

.event-image {
  border-radius: 0;
}

.event-date-badge .badge {
  text-align: center;
  min-width: 60px;
  font-weight: 600;
}

.event-meta {
  font-size: 0.9rem;
}

.event-attendees {
  margin-top: 1rem;
}

.avatar-stack {
  display: flex;
  margin-right: 0.5rem;
}

.avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid white;
  margin-left: -8px;
}

.avatar:first-child {
  margin-left: 0;
}

/* CTA Section */
.cta-section {
  background: var(--gradient-primary);
  color: white;
}

.cta-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>') no-repeat;
  background-size: cover;
}

.cta-buttons .btn {
  min-width: 200px;
  font-weight: 600;
  transition: var(--transition);
}

.cta-buttons .btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.trust-indicators {
  margin-top: 3rem;
}

.company-logos {
  gap: 2rem;
}

.company-logo-item {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.company-logo-item:hover {
  transform: scale(1.1);
}

/* Newsletter Section */
.newsletter-section {
  background-color: #f8f9fa;
}

.newsletter-form .input-group {
  max-width: 400px;
  margin: 0 auto;
}

.newsletter-form .form-control {
  border: 2px solid #e9ecef;
  padding: 0.75rem 1.25rem;
  font-size: 1rem;
}

.newsletter-form .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Footer */
.footer {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

.footer-brand {
  max-width: 300px;
}

.footer-title {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: white;
}

.footer-links {
  margin: 0;
  padding: 0;
}

.footer-link {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: var(--transition);
  display: inline-block;
  padding: 0.25rem 0;
}

.footer-link:hover {
  color: white;
  transform: translateX(5px);
}

.social-links {
  margin-top: 1rem;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  text-decoration: none;
  transition: var(--transition);
}

.social-link:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-3px);
}

.footer-divider {
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.footer-copyright {
  font-size: 0.9rem;
}

.footer-meta {
  font-size: 0.9rem;
}

/* Back to Top Button */
.back-to-top {
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  display: none;
  z-index: 1000;
  border: none;
  box-shadow: var(--shadow-lg);
  transition: var(--transition);
}

.back-to-top:hover {
  transform: translateY(-3px);
  box-shadow: 0 1rem 2rem rgba(13, 110, 253, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    min-height: 80vh;
    padding: 2rem 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .search-container {
    padding: 1rem;
  }

  .search-container .row {
    flex-direction: column;
  }

  .search-container .col-auto {
    margin-top: 1rem;
  }

  .hero-stats {
    margin-top: 2rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .company-logos {
    justify-content: center;
  }

  .footer .row > div {
    margin-bottom: 2rem;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .navbar-brand {
    font-size: 1.25rem;
  }

  .search-container .btn {
    width: 100%;
  }
}

/* Accessibility */
.visually-hidden-focusable:not(:focus):not(:focus-within) {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Focus styles for better accessibility */
.btn:focus,
.form-control:focus,
.nav-link:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Loading states */
.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Print styles */
@media print {
  .navbar,
  .footer,
  .back-to-top,
  .floating-elements {
    display: none !important;
  }

  .hero-section {
    min-height: auto;
    padding: 2rem 0;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }
}

/* Auth Pages Styles */
.auth-page {
  font-family: var(--font-primary);
  overflow-x: hidden;
}

.auth-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  z-index: 1;
}

.auth-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 2;
}

.auth-card {
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: var(--transition);
}

.auth-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 2rem 4rem rgba(0, 0, 0, 0.2);
}

.auth-title {
  font-family: var(--font-heading);
  color: var(--dark-color);
}

.auth-subtitle {
  font-size: 1rem;
}

.auth-icon {
  animation: pulse 2s infinite;
}

.auth-form .input-group-text {
  border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.auth-form .form-control {
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
}

.auth-form .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.auth-form .btn {
  font-size: 1rem;
  transition: var(--transition);
}

.auth-form .btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.divider {
  position: relative;
  text-align: center;
  margin: 1.5rem 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e9ecef;
}

.divider-text {
  background: white;
  padding: 0 1rem;
  font-size: 0.875rem;
}

.password-strength .progress {
  border-radius: 2px;
}

/* Role-specific styling */
.role-card {
  border: 2px solid transparent;
  transition: var(--transition);
  cursor: pointer;
}

.role-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-3px);
  box-shadow: var(--shadow);
}

.role-card.selected {
  border-color: var(--primary-color);
  background: rgba(13, 110, 253, 0.05);
}

.role-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
}

/* Responsive auth pages */
@media (max-width: 768px) {
  .auth-card .card-body {
    padding: 2rem !important;
  }

  .auth-title {
    font-size: 1.75rem;
  }

  .auth-form .form-control {
    padding: 0.875rem 1rem;
  }

  .auth-form .btn {
    padding: 0.875rem 1rem;
  }
}

@media (max-width: 576px) {
  .auth-card .card-body {
    padding: 1.5rem !important;
  }

  .auth-title {
    font-size: 1.5rem;
  }

  .floating-elements {
    display: none;
  }
}