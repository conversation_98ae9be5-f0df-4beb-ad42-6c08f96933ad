<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Login to your Univio account to access internships and career opportunities">
  <meta name="keywords" content="login, signin, univio, internships, career">
  
  <title>Login - Univio</title>
  
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="preconnect" href="https://cdn.jsdelivr.net">
  
  <!-- Modern fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Bootstrap 5.3 -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
  
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  
  <!-- Custom styles -->
  <link rel="stylesheet" href="style.css">
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
</head>
<body class="auth-page">
  <!-- Background Elements -->
  <div class="auth-background">
    <div class="auth-overlay"></div>
    <div class="floating-elements">
      <div class="floating-element floating-element-1"></div>
      <div class="floating-element floating-element-2"></div>
      <div class="floating-element floating-element-3"></div>
    </div>
  </div>

  <!-- Navigation -->
  <nav class="navbar navbar-expand-lg navbar-light bg-transparent position-absolute w-100" style="z-index: 1000;">
    <div class="container">
      <a class="navbar-brand d-flex align-items-center text-white" href="index.html">
        <i class="bi bi-rocket-takeoff-fill me-2 fs-3"></i>
        <span class="fw-bold fs-4">Univio</span>
      </a>
      <div class="navbar-nav ms-auto">
        <a class="nav-link text-white-75" href="index.html">
          <i class="bi bi-arrow-left me-1"></i>Back to Home
        </a>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="container-fluid d-flex align-items-center justify-content-center min-vh-100 position-relative" style="z-index: 100;">
    <div class="row w-100 justify-content-center">
      <div class="col-lg-4 col-md-6 col-sm-8">
        <div class="auth-card card border-0 shadow-lg">
          <div class="card-body p-5">
            <!-- Header -->
            <div class="text-center mb-4">
              <div class="auth-icon mb-3">
                <i class="bi bi-person-circle text-primary" style="font-size: 3rem;"></i>
              </div>
              <h2 class="auth-title fw-bold mb-2">Welcome Back</h2>
              <p class="auth-subtitle text-muted">Sign in to your account to continue</p>
            </div>

            <!-- Login Form -->
            <form class="auth-form" id="loginForm">
              <div class="mb-4">
                <label for="email" class="form-label fw-medium">Email Address</label>
                <div class="input-group">
                  <span class="input-group-text bg-light border-end-0">
                    <i class="bi bi-envelope text-muted"></i>
                  </span>
                  <input type="email" class="form-control border-start-0 ps-0" id="email" 
                         placeholder="Enter your email" required>
                </div>
              </div>

              <div class="mb-4">
                <label for="password" class="form-label fw-medium">Password</label>
                <div class="input-group">
                  <span class="input-group-text bg-light border-end-0">
                    <i class="bi bi-lock text-muted"></i>
                  </span>
                  <input type="password" class="form-control border-start-0 ps-0" id="password" 
                         placeholder="Enter your password" required>
                  <button class="btn btn-outline-secondary border-start-0" type="button" id="togglePassword">
                    <i class="bi bi-eye"></i>
                  </button>
                </div>
              </div>

              <div class="d-flex justify-content-between align-items-center mb-4">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="rememberMe">
                  <label class="form-check-label text-muted" for="rememberMe">
                    Remember me
                  </label>
                </div>
                <a href="#" class="text-decoration-none">Forgot password?</a>
              </div>

              <button type="submit" class="btn btn-primary w-100 py-3 fw-medium rounded-pill mb-4">
                <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
              </button>

              <!-- Divider -->
              <div class="divider mb-4">
                <span class="divider-text text-muted">or continue with</span>
              </div>

              <!-- Social Login -->
              <div class="row g-2 mb-4">
                <div class="col-6">
                  <button type="button" class="btn btn-outline-secondary w-100 py-2">
                    <i class="bi bi-google me-1"></i>Google
                  </button>
                </div>
                <div class="col-6">
                  <button type="button" class="btn btn-outline-secondary w-100 py-2">
                    <i class="bi bi-linkedin me-1"></i>LinkedIn
                  </button>
                </div>
              </div>

              <!-- Sign Up Link -->
              <div class="text-center">
                <p class="text-muted mb-0">
                  Don't have an account? 
                  <a href="register.html" class="text-decoration-none fw-medium">Create one</a>
                </p>
              </div>
            </form>
          </div>
        </div>

        <!-- Additional Info -->
        <div class="text-center mt-4">
          <p class="text-white-75 small">
            By signing in, you agree to our 
            <a href="#" class="text-white">Terms of Service</a> and 
            <a href="#" class="text-white">Privacy Policy</a>
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
  
  <script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
      const password = document.getElementById('password');
      const icon = this.querySelector('i');
      
      if (password.type === 'password') {
        password.type = 'text';
        icon.classList.remove('bi-eye');
        icon.classList.add('bi-eye-slash');
      } else {
        password.type = 'password';
        icon.classList.remove('bi-eye-slash');
        icon.classList.add('bi-eye');
      }
    });

    // Form submission
    document.getElementById('loginForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      // Add loading state
      const submitBtn = this.querySelector('button[type="submit"]');
      const originalText = submitBtn.innerHTML;
      submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin me-2"></i>Signing in...';
      submitBtn.disabled = true;
      
      // Simulate login process
      setTimeout(() => {
        // Redirect based on user type (this would normally come from server)
        const email = document.getElementById('email').value;
        if (email.includes('admin')) {
          window.location.href = 'admindashboard.html';
        } else if (email.includes('company')) {
          window.location.href = 'companydashboard.html';
        } else {
          window.location.href = 'studentdashboard.html';
        }
      }, 2000);
    });
  </script>

  <style>
    .spin {
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</body>
</html>
