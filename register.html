<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Create your Univio account to access thousands of internships and career opportunities">
  <meta name="keywords" content="register, signup, univio, internships, career, students">
  
  <title>Create Account - Univio</title>
  
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="preconnect" href="https://cdn.jsdelivr.net">
  
  <!-- Modern fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Bootstrap 5.3 -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
  
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  
  <!-- Custom styles -->
  <link rel="stylesheet" href="style.css">
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
</head>
<body class="auth-page">
  <!-- Background Elements -->
  <div class="auth-background">
    <div class="auth-overlay"></div>
    <div class="floating-elements">
      <div class="floating-element floating-element-1"></div>
      <div class="floating-element floating-element-2"></div>
      <div class="floating-element floating-element-3"></div>
    </div>
  </div>

  <!-- Navigation -->
  <nav class="navbar navbar-expand-lg navbar-light bg-transparent position-absolute w-100" style="z-index: 1000;">
    <div class="container">
      <a class="navbar-brand d-flex align-items-center text-white" href="index.html">
        <i class="bi bi-mortarboard-fill me-2 fs-3"></i>
        <span class="fw-bold fs-4">Univio</span>
      </a>
      <div class="navbar-nav ms-auto">
        <a class="nav-link text-white-75" href="index.html">
          <i class="bi bi-arrow-left me-1"></i>Back to Home
        </a>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="container-fluid d-flex align-items-center justify-content-center min-vh-100 position-relative py-5" style="z-index: 100;">
    <div class="row w-100 justify-content-center">
      <div class="col-lg-5 col-md-7 col-sm-9">
        <div class="auth-card card border-0 shadow-lg">
          <div class="card-body p-5">
            <!-- Header -->
            <div class="text-center mb-4">
              <div class="auth-icon mb-3">
                <i class="bi bi-person-plus-fill text-success" style="font-size: 3rem;"></i>
              </div>
              <h2 class="auth-title fw-bold mb-2">Create Your Account</h2>
              <p class="auth-subtitle text-muted">Join thousands of students finding their dream internships</p>
            </div>

            <!-- Registration Form -->
            <form class="auth-form" id="registerForm">
              <div class="row g-3">
                <div class="col-md-6">
                  <label for="firstName" class="form-label fw-medium">First Name</label>
                  <div class="input-group">
                    <span class="input-group-text bg-light border-end-0">
                      <i class="bi bi-person text-muted"></i>
                    </span>
                    <input type="text" class="form-control border-start-0 ps-0" id="firstName" 
                           placeholder="First name" required>
                  </div>
                </div>
                
                <div class="col-md-6">
                  <label for="lastName" class="form-label fw-medium">Last Name</label>
                  <div class="input-group">
                    <span class="input-group-text bg-light border-end-0">
                      <i class="bi bi-person text-muted"></i>
                    </span>
                    <input type="text" class="form-control border-start-0 ps-0" id="lastName" 
                           placeholder="Last name" required>
                  </div>
                </div>
              </div>

              <div class="mb-4">
                <label for="email" class="form-label fw-medium">Email Address</label>
                <div class="input-group">
                  <span class="input-group-text bg-light border-end-0">
                    <i class="bi bi-envelope text-muted"></i>
                  </span>
                  <input type="email" class="form-control border-start-0 ps-0" id="email" 
                         placeholder="Enter your email" required>
                </div>
              </div>

              <div class="mb-4">
                <label for="password" class="form-label fw-medium">Password</label>
                <div class="input-group">
                  <span class="input-group-text bg-light border-end-0">
                    <i class="bi bi-lock text-muted"></i>
                  </span>
                  <input type="password" class="form-control border-start-0 ps-0" id="password" 
                         placeholder="Create a strong password" required>
                  <button class="btn btn-outline-secondary border-start-0" type="button" id="togglePassword">
                    <i class="bi bi-eye"></i>
                  </button>
                </div>
                <div class="password-strength mt-2">
                  <div class="progress" style="height: 4px;">
                    <div class="progress-bar" id="passwordStrength" role="progressbar" style="width: 0%"></div>
                  </div>
                  <small class="text-muted" id="passwordHelp">Use 8+ characters with letters, numbers & symbols</small>
                </div>
              </div>

              <div class="mb-4">
                <label for="role" class="form-label fw-medium">I am a...</label>
                <div class="input-group">
                  <span class="input-group-text bg-light border-end-0">
                    <i class="bi bi-briefcase text-muted"></i>
                  </span>
                  <select class="form-select border-start-0" id="role" required>
                    <option value="">Select your role</option>
                    <option value="student">Student looking for internships</option>
                    <option value="company">Company hiring interns</option>
                    <option value="university">University/Institution</option>
                  </select>
                </div>
              </div>

              <div class="mb-4">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                  <label class="form-check-label text-muted" for="agreeTerms">
                    I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> and 
                    <a href="#" class="text-decoration-none">Privacy Policy</a>
                  </label>
                </div>
              </div>

              <div class="mb-4">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="newsletter">
                  <label class="form-check-label text-muted" for="newsletter">
                    Send me updates about new opportunities and events
                  </label>
                </div>
              </div>

              <button type="submit" class="btn btn-success w-100 py-3 fw-medium rounded-pill mb-4">
                <i class="bi bi-person-plus me-2"></i>Create Account
              </button>

              <!-- Divider -->
              <div class="divider mb-4">
                <span class="divider-text text-muted">or sign up with</span>
              </div>

              <!-- Social Registration -->
              <div class="row g-2 mb-4">
                <div class="col-6">
                  <button type="button" class="btn btn-outline-secondary w-100 py-2">
                    <i class="bi bi-google me-1"></i>Google
                  </button>
                </div>
                <div class="col-6">
                  <button type="button" class="btn btn-outline-secondary w-100 py-2">
                    <i class="bi bi-linkedin me-1"></i>LinkedIn
                  </button>
                </div>
              </div>

              <!-- Sign In Link -->
              <div class="text-center">
                <p class="text-muted mb-0">
                  Already have an account? 
                  <a href="login_new.html" class="text-decoration-none fw-medium">Sign in</a>
                </p>
              </div>
            </form>
          </div>
        </div>

        <!-- Additional Info -->
        <div class="text-center mt-4">
          <p class="text-white-75 small">
            Join 50,000+ students and professionals already using Univio
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
  
  <script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
      const password = document.getElementById('password');
      const icon = this.querySelector('i');
      
      if (password.type === 'password') {
        password.type = 'text';
        icon.classList.remove('bi-eye');
        icon.classList.add('bi-eye-slash');
      } else {
        password.type = 'password';
        icon.classList.remove('bi-eye-slash');
        icon.classList.add('bi-eye');
      }
    });

    // Password strength indicator
    document.getElementById('password').addEventListener('input', function() {
      const password = this.value;
      const strengthBar = document.getElementById('passwordStrength');
      const helpText = document.getElementById('passwordHelp');
      
      let strength = 0;
      let feedback = '';
      
      if (password.length >= 8) strength += 25;
      if (/[a-z]/.test(password)) strength += 25;
      if (/[A-Z]/.test(password)) strength += 25;
      if (/[0-9]/.test(password)) strength += 25;
      
      strengthBar.style.width = strength + '%';
      
      if (strength < 50) {
        strengthBar.className = 'progress-bar bg-danger';
        feedback = 'Weak password';
      } else if (strength < 75) {
        strengthBar.className = 'progress-bar bg-warning';
        feedback = 'Good password';
      } else {
        strengthBar.className = 'progress-bar bg-success';
        feedback = 'Strong password';
      }
      
      helpText.textContent = feedback || 'Use 8+ characters with letters, numbers & symbols';
    });

    // Form submission
    document.getElementById('registerForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      // Add loading state
      const submitBtn = this.querySelector('button[type="submit"]');
      const originalText = submitBtn.innerHTML;
      submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin me-2"></i>Creating account...';
      submitBtn.disabled = true;
      
      // Simulate registration process
      setTimeout(() => {
        alert('Account created successfully! Please check your email to verify your account.');
        window.location.href = 'login_new.html';
      }, 2000);
    });
  </script>

  <style>
    .spin {
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</body>
</html>
